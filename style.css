/* Import Google Fonts for better typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

* {
    box-sizing: border-box;
}

body {
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #0f0f0f 100%);
    background-attachment: fixed;
    color: #e8e8e8;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 1.1em;
    font-weight: 400;
    line-height: 1.8;
    margin: 0;
    padding: 40px 20px 140px 20px;
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    max-width: 900px;
    margin: 0 auto;
    background: rgba(25, 25, 25, 0.8);
    border-radius: 16px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* --- HUD --- */
.hud {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.95) 0%, rgba(15, 15, 15, 0.98) 100%);
    border-top: 3px solid #d4af37;
    padding: 16px 24px;
    box-sizing: border-box;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-size: 0.95em;
    font-weight: 500;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    z-index: 1000;
    backdrop-filter: blur(20px);
    box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.5);
}

.hud-item {
    margin: 8px 20px;
    color: #f4d03f;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    transition: all 0.3s ease;
}

.hud-item:hover {
    color: #fff;
    transform: translateY(-1px);
}

.hud-item strong {
    color: #b8b8b8;
    font-weight: 600;
    margin-right: 8px;
}

.system-button {
    cursor: pointer;
    border: 1px solid #d4af37;
    padding: 5px 10px;
    border-radius: 5px;
}

.system-button:hover {
    background-color: rgba(212, 175, 55, 0.1);
}

/* --- Story & Prompts --- */
.system-prompt {
    background: linear-gradient(135deg, rgba(20, 40, 80, 0.6) 0%, rgba(30, 50, 90, 0.4) 100%);
    border: 2px solid rgba(102, 170, 255, 0.6);
    border-radius: 12px;
    padding: 24px;
    margin: 32px 0;
    position: relative;
    box-shadow: 0 8px 32px rgba(102, 170, 255, 0.1);
    backdrop-filter: blur(5px);
}

.system-prompt::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(45deg, #66aaff, #4488dd, #66aaff);
    border-radius: 12px;
    z-index: -1;
    opacity: 0.3;
}

.prompt-title {
    color: #88ccff;
    font-weight: 600;
    font-size: 1.1em;
    margin-bottom: 16px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    letter-spacing: 0.5px;
}

.reward {
    color: #4ade80;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.penalty {
    color: #f87171;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* --- Choices --- */
.choice-section {
    margin-top: 40px;
    padding: 32px;
    background: linear-gradient(135deg, rgba(30, 30, 30, 0.8) 0%, rgba(40, 40, 40, 0.6) 100%);
    border-left: 6px solid #d4af37;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.choice-button {
    display: block;
    width: 100%;
    margin: 20px 0;
    padding: 20px 24px;
    background: linear-gradient(135deg, #d4af37 0%, #b38f00 100%);
    color: #000;
    text-align: center;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.05em;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 16px rgba(212, 175, 55, 0.3);
    position: relative;
    overflow: hidden;
}

.choice-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.choice-button:hover {
    background: linear-gradient(135deg, #f4d03f 0%, #d4af37 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(212, 175, 55, 0.4);
}

.choice-button:hover::before {
    left: 100%;
}

.choice-button:active {
    transform: translateY(0);
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

/* --- Special Text --- */
p {
    margin: 1.2em 0;
    text-align: justify;
}

em {
    color: #c0c0c0;
    font-style: italic;
    font-weight: 300;
}

strong {
    color: #ff7b7b;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.character-name {
    color: #d4af37;
    cursor: pointer;
    font-weight: 600;
    text-decoration: none;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    padding: 2px 4px;
    border-radius: 4px;
}

.character-name:hover {
    color: #f4d03f;
    background: rgba(212, 175, 55, 0.1);
    border-bottom-color: #d4af37;
    text-shadow: 0 1px 4px rgba(212, 175, 55, 0.5);
}

/* --- Modals --- */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(8px);
    animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-content {
    background: linear-gradient(135deg, #1f1f1f 0%, #2a2a2a 100%);
    margin: 8% auto;
    padding: 32px;
    border: 2px solid #d4af37;
    width: 90%;
    max-width: 700px;
    border-radius: 16px;
    font-family: 'Inter', sans-serif;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);
    position: relative;
    animation: slideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-header {
    color: #d4af37;
    font-size: 1.4em;
    font-weight: 600;
    margin-bottom: 24px;
    border-bottom: 2px solid rgba(212, 175, 55, 0.3);
    padding-bottom: 16px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.close-button {
    color: #888;
    position: absolute;
    top: 20px;
    right: 24px;
    font-size: 28px;
    font-weight: bold;
    line-height: 1;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 4px 8px;
    border-radius: 50%;
}

.close-button:hover,
.close-button:focus {
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
}

.modal-body ul {
    list-style: none;
    padding: 0;
}

.modal-body li {
    background: rgba(255, 255, 255, 0.05);
    margin-bottom: 10px;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #d4af37;
}

.system-menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.system-menu-button {
    padding: 25px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    text-align: center;
    font-family: 'JetBrains Mono', monospace;
    font-size: 1.1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.system-menu-button:hover {
    background: rgba(212, 175, 55, 0.1);
    border-color: #d4af37;
    transform: translateY(-5px);
    color: #f4d03f;
}

.system-menu-button.disabled {
    color: #666;
    cursor: not-allowed;
    background: rgba(0, 0, 0, 0.2);
}
.system-menu-button.disabled:hover {
    transform: none;
    border-color: rgba(255, 255, 255, 0.1);
}

@keyframes fadeIn {
    from { opacity: 0; } to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; } to { opacity: 0; }
}

@keyframes slideIn {
    from { opacity: 0; transform: translateY(-30px) scale(0.95); }
    to { opacity: 1; transform: translateY(0) scale(1); }
}

/* Scene content animations */
.scene-content {
    animation: slideInContent 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInContent {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

/* --- Responsive Design --- */
@media (max-width: 768px) {
    body {
        padding: 20px 16px 160px 16px; font-size: 1em;
    }
    .container {
        padding: 24px; border-radius: 12px;
    }
    .hud {
        padding: 12px 16px; font-size: 0.85em;
    }
    .hud-item {
        margin: 6px 12px;
    }
    .choice-button {
        padding: 16px 20px; font-size: 1em;
    }
    .modal-content {
        margin: 5% auto; padding: 24px; width: 95%;
    }
    .system-prompt {
        padding: 20px; margin: 24px 0;
    }
}