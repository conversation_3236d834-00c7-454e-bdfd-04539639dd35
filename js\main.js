import { player } from './player.js';
import { renderScene, showModal, closeModal, setCurrentModals, initializeUI } from './ui.js';

let currentChapterData;
let currentChapterNumber = 1;

// Make UI functions and player globally accessible for inline HTML onclicks and onLoad functions
window.ui = { showModal, closeModal };
window.player = player;
window.main = { makeChoice }; // Expose makeChoice globally

export async function loadChapter(chapterNumber) {
    try {
        const chapterModule = await import(`../story/chapter${chapterNumber}.js`);
        currentChapterData = chapterModule.chapterData;
        setCurrentModals(chapterModule.modals || {});

        console.log(`Chapter ${chapterNumber} loaded successfully.`);
        renderScene('start', currentChapterData['start']);
    } catch (error) {
        console.error("Failed to load chapter:", error);
        const storyContainer = document.getElementById('story-container');
        storyContainer.innerHTML = `<p>Error: Could not load Chapter ${chapterNumber}. File may be missing or contain an error.</p>`;
    }
}

function makeChoice(choice) {
    if (choice.leadsTo === "LOAD_NEXT_CHAPTER") {
        currentChapterNumber = choice.chapter;
        loadChapter(currentChapterNumber);
    } else {
        renderScene(choice.leadsTo, currentChapterData[choice.leadsTo]);
    }
}

// Initial game start
initializeUI();
loadChapter(1);