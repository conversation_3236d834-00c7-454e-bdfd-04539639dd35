import { player } from './player.js';
import { makeChoice } from './main.js';

const storyContainer = document.getElementById('story-container');
const hudElements = {
    location: document.getElementById('hud-location'),
    time: document.getElementById('hud-time'),
    cp: document.getElementById('hud-cp'),
    sp: document.getElementById('hud-sp'),
    dxp: document.getElementById('hud-dxp'),
};
const modalContainer = document.getElementById('modal-container');
const hudContainer = document.getElementById('hud');

let currentModals = {};

export function initializeUI() {
    const systemButton = document.createElement('div');
    systemButton.className = 'hud-item system-button';
    systemButton.innerHTML = `<strong>[SYSTEM]</strong>`;
    systemButton.onclick = () => showSystemMenu();
    hudContainer.appendChild(systemButton);
}

export function updateHUD() {
    hudElements.location.textContent = player.location;
    hudElements.time.textContent = player.time;
    hudElements.cp.textContent = player.stats.cp;
    hudElements.sp.textContent = player.stats.sp;
    hudElements.dxp.textContent = player.stats.dxp;
}

export function renderScene(sceneId, sceneData) {
    if (!sceneData) {
        console.error(`Scene data not found for sceneId: ${sceneId}`);
        return;
    }

    storyContainer.style.opacity = '0';
    storyContainer.style.transform = 'translateY(20px)';

    setTimeout(() => {
        if (sceneData.onLoad) {
            sceneData.onLoad();
        }

        let html = `<div id="${sceneId}" class="scene-content">${sceneData.text}</div>`;

        if (sceneData.choices && sceneData.choices.length > 0) {
            html += `<div class="choice-section">`;
            sceneData.choices.forEach((choice, index) => {
                html += `<a href="#" class="choice-button" data-choice-index="${index}">${choice.label}</a>`;
            });
            html += `</div>`;
        }

        storyContainer.innerHTML = html;
        addChoiceListeners(sceneData.choices);
        updateHUD();

        storyContainer.style.transition = 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
        storyContainer.style.opacity = '1';
        storyContainer.style.transform = 'translateY(0)';

        window.scrollTo({ top: 0, behavior: 'smooth' });
    }, 150);
}

function addChoiceListeners(choices) {
    if (!choices) return;
    const buttons = document.querySelectorAll('.choice-button');
    buttons.forEach((button, index) => {
        button.style.opacity = '0';
        button.style.transform = 'translateY(20px)';
        setTimeout(() => {
            button.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
            button.style.opacity = '1';
            button.style.transform = 'translateY(0)';
        }, 300 + (index * 100));

        button.addEventListener('click', (event) => {
            event.preventDefault();
            button.style.transform = 'scale(0.95)';
            setTimeout(() => { button.style.transform = 'scale(1)'; }, 150);
            buttons.forEach(btn => {
                btn.style.pointerEvents = 'none';
                btn.style.opacity = '0.6';
            });
            setTimeout(() => { makeChoice(choices[index]); }, 200);
        });
    });
}

export function showModal(modalId) {
    const modalData = currentModals[modalId];
    if (!modalData) return;
    renderModal(modalId, modalData.title, modalData.content);
}

export function closeModal(modalId) {
    const modal = document.getElementById(modalId + 'Modal');
    if (modal) {
        modal.style.animation = 'fadeOut 0.3s ease-out';
        setTimeout(() => {
            modal.style.display = 'none';
            modalContainer.innerHTML = '';
        }, 290);
    } else {
        modalContainer.innerHTML = '';
    }
}

export function setCurrentModals(modals) {
    currentModals = modals;
}

function renderModal(id, title, content) {
    let modalHTML = `
        <div id="${id}Modal" class="modal" style="display:block;">
            <div class="modal-content">
                <span class="close-button" onclick="window.ui.closeModal('${id}')">×</span>
                <div class="modal-header">${title}</div>
                <div class="modal-body">${content}</div>
            </div>
        </div>
    `;
    modalContainer.innerHTML = modalHTML;
    const modal = document.getElementById(id + 'Modal');
    modal.addEventListener('click', (event) => {
        if (event.target === modal) { closeModal(id); }
    });
    document.addEventListener('keydown', function escapeHandler(event) {
        if (event.key === 'Escape') {
            closeModal(id);
            document.removeEventListener('keydown', escapeHandler);
        }
    });
}

// --- System Menu Functions ---
function showSystemMenu() {
    const content = `
        <div class="system-menu-grid">
            <div class="system-menu-button" onclick="window.ui.showInventory()">[ Inventory ]</div>
            <div class="system-menu-button" onclick="window.ui.showSkills()">[ Skills ]</div>
            <div class="system-menu-button disabled">[ System Shop ]</div>
            <div class="system-menu-button disabled">[ Wealth Conversion ]</div>
        </div>
    `;
    renderModal('systemMenu', '[ System Mainframe ]', content);
}

function showInventory() {
    let content = '<ul>';
    if (player.inventory.length === 0) {
        content += '<li>Your inventory is empty.</li>';
    } else {
        player.inventory.forEach(item => {
            content += `<li>${item.item} (x${item.quantity})</li>`;
        });
    }
    content += '</ul>';
    renderModal('inventory', '[ Player Inventory ]', content);
}

function showSkills() {
    let content = '<ul>';
    if (player.skills.length === 0) {
        content += '<li>You have no skills.</li>';
    } else {
        player.skills.forEach(skill => {
            content += `<li>${skill}</li>`;
        });
    }
    content += '</ul>';
    renderModal('skills', '[ Unlocked Skills ]', content);
}

// Expose functions to global window object
window.ui = { ...window.ui, showSystemMenu, showInventory, showSkills };